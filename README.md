# Timesheet vs Pay Slip Analyzer

A Python application built with Flet framework to automate the comparison between Excel timesheets and PDF pay slips, ensuring accuracy in declared working hours.

## Features

- **Excel Timesheet Parsing**: Extracts daily working hours from Excel timesheet files
- **PDF Pay Slip Analysis**: Extracts day codes (Z, R, F, etc.) from PDF pay slips and calculates paid hours
- **Automated Comparison**: Compares total hours between both documents
- **Modern GUI**: User-friendly interface built with Flet framework
- **Detailed Results**: Shows comprehensive analysis with employee info, hours comparison, and discrepancy detection

## Supported Day Codes

The application recognizes these paid day codes from PDF pay slips:
- **Z**: Regular work day (8 hours)
- **R**: Regular work day (8 hours)
- **F**: Holiday/Festivity (8 hours)
- **FE**: Extra holiday (8 hours)
- **DI**: Paid day (8 hours)
- **RD**: Reduced day but paid (8 hours)
- **ED**: Extra day (8 hours)

## Installation

1. Install Python 3.8 or higher
2. Install required packages:
```bash
pip install -r requirements.txt
```

## Usage

### Running the Application

```bash
# Run the improved analyzer
python improved_timesheet_analyzer.py

# Or run the basic version
python timesheet_analyzer.py
```

### Steps to Analyze

1. **Launch the Application**: Run the Python script
2. **Select Excel File**: Click "Select Excel Timesheet" and choose your .xlsx file
3. **Select PDF File**: Click "Select PDF Pay Slip" and choose your .pdf file
4. **Analyze**: Click "Analyze & Compare" to start the comparison
5. **Review Results**: Check the detailed results showing any discrepancies

### Testing Data Structure

To understand your file formats, you can run the test script:
```bash
python test_data_extraction.py
```

## File Format Requirements

### Excel Timesheet Format
- Must contain employee name (look for "Nominativo" field)
- Must have day headers (numbers 1-31)
- Must have activity rows with daily hours
- Activities like "Assenza non retribuita" are excluded from calculations

### PDF Pay Slip Format
- Must contain employee name
- Must contain month/year information
- Must contain day codes (Z, R, F, etc.) indicating paid days
- Standard Italian pay slip format expected

## Example Results

The application provides detailed analysis including:
- ✅ **Match Status**: Whether hours match within tolerance
- 👤 **Employee Information**: Names from both documents
- ⏰ **Hours Comparison**: Total hours from Excel vs PDF
- 📅 **Days Comparison**: Working days vs paid days
- 📊 **Difference Analysis**: Absolute and percentage differences
- 💡 **Interpretation**: Explanation of any discrepancies

## Troubleshooting

### Common Issues

1. **"Could not find day header row"**: Check Excel format, ensure day numbers 1-31 are present
2. **"No day codes found"**: Verify PDF contains recognizable day codes (Z, R, F, etc.)
3. **"Analysis failed"**: Check file formats and ensure files are not corrupted

### File Format Tips

- Ensure Excel files are in .xlsx or .xls format
- Ensure PDF files are text-readable (not scanned images)
- Check that employee names are clearly visible in both documents

## Development

### Project Structure
```
automation/
├── requirements.txt              # Dependencies
├── timesheet_analyzer.py        # Basic analyzer
├── improved_timesheet_analyzer.py # Enhanced analyzer
├── test_data_extraction.py      # Data structure testing
└── README.md                    # This file
```

### Key Components

1. **TimesheetAnalyzer Class**: Core logic for parsing and comparison
2. **TimesheetAnalyzerApp Class**: Flet GUI application
3. **Data Parsers**: Separate methods for Excel and PDF parsing
4. **Comparison Engine**: Logic to match and validate hours

## Future Enhancements

- Support for different timesheet formats
- OCR support for scanned PDFs
- Batch processing of multiple files
- Export results to Excel/PDF reports
- Configuration for different day code mappings

## License

This project is for internal use in timesheet verification processes. 