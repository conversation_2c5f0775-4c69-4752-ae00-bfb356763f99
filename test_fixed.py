from fixed_analyzer import FixedTimesheetAnalyzer
import traceback

def test_fixed_analyzer():
    """Test the fixed analyzer"""
    analyzer = FixedTimesheetAnalyzer()
    
    print("=== TESTING FIXED ANALYZER ===\n")
    
    # Test Excel parsing
    print("1. Testing fixed Excel parsing...")
    try:
        excel_data = analyzer.parse_excel_timesheet("GENNAIO 2025.xlsx")
        print(f"✅ Excel parsing successful!")
        print(f"   Employee: {excel_data['employee_name']}")
        print(f"   Total hours: {excel_data['total_hours']:.1f}")
        print(f"   Working days: {excel_data['working_days']}")
        print(f"   Activities found: {len(excel_data['activities'])}")
        for activity, hours in excel_data['activities'].items():
            total_activity_hours = sum(hours)
            if total_activity_hours > 0:
                print(f"   - {activity}: {total_activity_hours:.1f}h")
        print()
    except Exception as e:
        print(f"❌ Excel parsing failed: {e}")
        traceback.print_exc()
        print()
    
    # Test PDF parsing
    print("2. Testing fixed PDF parsing...")
    try:
        pdf_data = analyzer.parse_pdf_payslip("D0531_STAMPA LUL_01.2025.pdf")
        print(f"✅ PDF parsing successful!")
        print(f"   Employee: {pdf_data['employee_name']}")
        print(f"   Period: {pdf_data['month_year']}")
        print(f"   Total paid hours: {pdf_data['total_paid_hours']}")
        print(f"   Total paid days: {pdf_data['total_paid_days']}")
        print(f"   Day codes found:")
        for code, count in pdf_data['day_codes_count'].items():
            print(f"   - {code}: {count} times")
        print()
    except Exception as e:
        print(f"❌ PDF parsing failed: {e}")
        traceback.print_exc()
        print()
    
    # Test comparison if both successful
    print("3. Testing fixed comparison...")
    try:
        if 'excel_data' in locals() and 'pdf_data' in locals():
            analyzer.excel_data = excel_data
            analyzer.pdf_data = pdf_data
            comparison = analyzer.compare_data()
            print(f"✅ Comparison successful!")
            print(f"   Excel employee: {comparison['excel_employee']}")
            print(f"   PDF employee: {comparison['pdf_employee']}")
            print(f"   Excel hours: {comparison['excel_total_hours']:.1f}h")
            print(f"   PDF hours: {comparison['pdf_total_hours']:.1f}h")
            print(f"   Difference: {comparison['difference_hours']:+.1f}h")
            print(f"   Match: {'✅ YES' if comparison['is_match'] else '❌ NO'}")
        else:
            print("❌ Cannot test comparison - parsing failed")
    except Exception as e:
        print(f"❌ Comparison failed: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    test_fixed_analyzer() 