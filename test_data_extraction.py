import pandas as pd
import pdfplumber
import sys
from pathlib import Path

def test_excel_structure():
    """Test reading the Excel timesheet"""
    print("=== TESTING EXCEL FILE ===")
    try:
        file_path = "GENNAIO 2025.xlsx"
        if not Path(file_path).exists():
            print(f"Excel file not found: {file_path}")
            return
        
        # Read the Excel file
        excel_file = pd.ExcelFile(file_path)
        print(f"Sheet names: {excel_file.sheet_names}")
        
        # Read first sheet
        df = pd.read_excel(file_path, sheet_name=0, header=None)
        print(f"Excel shape: {df.shape}")
        print("\nFirst 15 rows:")
        print(df.head(15).to_string())
        
        # Look for patterns
        print("\n=== LOOKING FOR DAY HEADERS ===")
        for idx, row in df.iterrows():
            if idx > 50:  # Don't check too many rows
                break
            # Check if row contains day numbers
            day_count = 0
            for cell in row:
                if pd.notna(cell):
                    try:
                        val = str(cell).strip()
                        if val.isdigit() and 1 <= int(val) <= 31:
                            day_count += 1
                    except:
                        pass
            if day_count >= 10:  # If we find at least 10 day numbers
                print(f"Potential day header row {idx}: {row.tolist()[:10]}...")
                # Show next few rows as they might contain the data
                print("Next 5 rows:")
                for i in range(idx+1, min(idx+6, len(df))):
                    if i < len(df):
                        print(f"Row {i}: {df.iloc[i].tolist()[:10]}...")
                break
        
    except Exception as e:
        print(f"Error reading Excel: {e}")

def test_pdf_structure():
    """Test reading the PDF payslip"""
    print("\n=== TESTING PDF FILE ===")
    try:
        file_path = "D0531_STAMPA LUL_01.2025.pdf"
        if not Path(file_path).exists():
            print(f"PDF file not found: {file_path}")
            return
        
        with pdfplumber.open(file_path) as pdf:
            print(f"Number of pages: {len(pdf.pages)}")
            
            # Extract text from first page
            page = pdf.pages[0]
            text = page.extract_text()
            
            print("First 1000 characters of PDF:")
            print(text[:1000])
            print("...")
            
            # Look for key patterns
            print("\n=== LOOKING FOR KEY PATTERNS ===")
            lines = text.split('\n')
            for i, line in enumerate(lines[:30]):  # First 30 lines
                line = line.strip()
                if line:
                    print(f"Line {i:2d}: {line}")
            
            # Look for day codes
            print("\n=== LOOKING FOR DAY CODES ===")
            import re
            codes = ['Z', 'R', 'F', 'FE', 'DI', 'RD', 'ED']
            for code in codes:
                matches = re.findall(rf'\b{code}\b', text)
                if matches:
                    print(f"Found code '{code}': {len(matches)} times")
        
    except Exception as e:
        print(f"Error reading PDF: {e}")

if __name__ == "__main__":
    test_excel_structure()
    test_pdf_structure() 