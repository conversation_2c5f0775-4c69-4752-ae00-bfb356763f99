import flet as ft
import pandas as pd
import pdfplumber
import re
from datetime import datetime, timedelta
from pathlib import Path
import logging
from typing import Dict, List, Tuple, Optional

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TimesheetAnalyzer:
    """Main class for analyzing timesheets and pay slips"""
    
    def __init__(self):
        self.paid_day_codes = {'Z', 'R', 'F', 'FE', 'DI', 'RD', 'ED'}  # Codes that represent 8-hour paid days
        self.excel_data = None
        self.pdf_data = None
        
    def parse_excel_timesheet(self, file_path: str) -> Dict:
        """Parse Excel timesheet and extract daily hours"""
        try:
            # Read Excel file with multiple sheets if needed
            excel_file = pd.ExcelFile(file_path)
            sheet_names = excel_file.sheet_names
            
            # Try to find the timesheet data (usually in first sheet)
            df = pd.read_excel(file_path, sheet_name=0, header=None)
            
            # Find the row with days (usually contains numbers 1-31)
            day_row_idx = None
            for idx, row in df.iterrows():
                # Look for a row that contains day numbers
                if any(str(cell).strip().isdigit() and 1 <= int(str(cell).strip()) <= 31 
                      for cell in row if pd.notna(cell)):
                    day_row_idx = idx
                    break
            
            if day_row_idx is None:
                raise ValueError("Could not find day headers in Excel file")
            
            # Extract daily hours for different activities
            activities_data = {}
            
            # Look for activity rows (rows with activity names)
            for idx in range(day_row_idx + 1, len(df)):
                row = df.iloc[idx]
                activity_name = str(row.iloc[0]) if pd.notna(row.iloc[0]) else ""
                
                if activity_name and not activity_name.lower().startswith('totale'):
                    daily_hours = []
                    # Extract hours for each day (columns with day numbers)
                    for col_idx in range(1, min(32, len(row))):  # Days 1-31
                        cell_value = row.iloc[col_idx]
                        if pd.notna(cell_value):
                            try:
                                hours = float(cell_value)
                                daily_hours.append(hours)
                            except:
                                daily_hours.append(0)
                        else:
                            daily_hours.append(0)
                    
                    activities_data[activity_name] = daily_hours
            
            return {
                'activities': activities_data,
                'total_days': len(daily_hours) if daily_hours else 31,
                'file_path': file_path
            }
            
        except Exception as e:
            logger.error(f"Error parsing Excel file: {e}")
            raise

    def parse_pdf_payslip(self, file_path: str) -> Dict:
        """Parse PDF pay slip and extract working day information"""
        try:
            with pdfplumber.open(file_path) as pdf:
                full_text = ""
                for page in pdf.pages:
                    full_text += page.extract_text() + "\n"
            
            # Extract employee info
            employee_name = self._extract_employee_name(full_text)
            month_year = self._extract_month_year(full_text)
            
            # Extract working day codes
            day_codes = self._extract_day_codes(full_text)
            
            # Calculate paid days based on codes
            paid_days = self._calculate_paid_days(day_codes)
            
            return {
                'employee_name': employee_name,
                'month_year': month_year,
                'day_codes': day_codes,
                'paid_days': paid_days,
                'total_paid_hours': paid_days * 8,  # Assuming 8 hours per paid day
                'file_path': file_path
            }
            
        except Exception as e:
            logger.error(f"Error parsing PDF file: {e}")
            raise

    def _extract_employee_name(self, text: str) -> str:
        """Extract employee name from PDF text"""
        # Look for common patterns in Italian pay slips
        patterns = [
            r'(?:COGNOME E NOME|Nome|NOME)\s*:?\s*([A-Z\s]+?)(?:\n|$)',
            r'([A-Z]+\s+[A-Z]+(?:\s+[A-Z]+)?)\s*(?:CF:|Codice)',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1).strip()
        
        return "Unknown Employee"

    def _extract_month_year(self, text: str) -> str:
        """Extract month and year from PDF text"""
        # Look for date patterns
        patterns = [
            r'(\w+)\s+(\d{4})',  # Gennaio 2025
            r'(\d{1,2})/(\d{4})',  # 01/2025
            r'MESE\s*:?\s*(\w+)',  # MESE: Gennaio
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return f"{match.group(1)} {match.group(2) if len(match.groups()) > 1 else ''}"
        
        return "Unknown Period"

    def _extract_day_codes(self, text: str) -> List[str]:
        """Extract day codes (Z, R, F, etc.) from PDF text"""
        # Look for patterns that indicate daily codes
        # This is a simplified extraction - you may need to adjust based on actual PDF format
        codes = []
        
        # Split text into lines and look for code patterns
        lines = text.split('\n')
        for line in lines:
            # Look for single letter codes that might represent day types
            found_codes = re.findall(r'\b([A-Z]{1,2})\b', line)
            for code in found_codes:
                if code in self.paid_day_codes:
                    codes.append(code)
        
        return codes

    def _calculate_paid_days(self, day_codes: List[str]) -> int:
        """Calculate total paid days from day codes"""
        return len([code for code in day_codes if code in self.paid_day_codes])

    def compare_data(self) -> Dict:
        """Compare Excel timesheet data with PDF pay slip data"""
        if not self.excel_data or not self.pdf_data:
            raise ValueError("Both Excel and PDF data must be loaded first")
        
        # Calculate total hours from Excel
        excel_total_hours = 0
        for activity, daily_hours in self.excel_data['activities'].items():
            if 'assenza non retribuita' not in activity.lower():  # Exclude unpaid absences
                excel_total_hours += sum(daily_hours)
        
        # Get paid hours from PDF
        pdf_total_hours = self.pdf_data['total_paid_hours']
        
        # Calculate difference
        difference = excel_total_hours - pdf_total_hours
        
        return {
            'excel_total_hours': excel_total_hours,
            'pdf_total_hours': pdf_total_hours,
            'difference': difference,
            'percentage_difference': (difference / pdf_total_hours * 100) if pdf_total_hours > 0 else 0,
            'match': abs(difference) < 1,  # Consider a match if difference is less than 1 hour
        }


class TimesheetApp:
    """Flet GUI application for timesheet analysis"""
    
    def __init__(self, page: ft.Page):
        self.page = page
        self.analyzer = TimesheetAnalyzer()
        self.setup_page()
        self.create_ui()

    def setup_page(self):
        """Configure the main page"""
        self.page.title = "Timesheet Analyzer"
        self.page.theme_mode = ft.ThemeMode.LIGHT
        self.page.window_width = 900
        self.page.window_height = 700
        self.page.window_resizable = True

    def create_ui(self):
        """Create the user interface"""
        # Title
        title = ft.Text(
            "Timesheet vs Pay Slip Analyzer",
            size=24,
            weight=ft.FontWeight.BOLD,
            color=ft.colors.BLUE_800
        )

        # File selection section
        self.excel_file_text = ft.Text("No Excel file selected", color=ft.colors.GREY_600)
        self.pdf_file_text = ft.Text("No PDF file selected", color=ft.colors.GREY_600)
        
        excel_btn = ft.ElevatedButton(
            "Select Excel File",
            icon=ft.icons.FOLDER_OPEN,
            on_click=self.pick_excel_file
        )
        
        pdf_btn = ft.ElevatedButton(
            "Select PDF File", 
            icon=ft.icons.FOLDER_OPEN,
            on_click=self.pick_pdf_file
        )

        # Analysis button
        self.analyze_btn = ft.ElevatedButton(
            "Analyze Files",
            icon=ft.icons.ANALYTICS,
            on_click=self.analyze_files,
            disabled=True,
            style=ft.ButtonStyle(
                color=ft.colors.WHITE,
                bgcolor=ft.colors.GREEN_600
            )
        )

        # Results section
        self.results_container = ft.Container(
            content=ft.Text("Results will appear here after analysis"),
            padding=20,
            border=ft.border.all(1, ft.colors.GREY_400),
            border_radius=10,
            bgcolor=ft.colors.GREY_50
        )

        # Progress indicator
        self.progress_ring = ft.ProgressRing(visible=False)

        # Layout
        self.page.add(
            ft.Column([
                ft.Container(title, padding=ft.padding.only(bottom=20)),
                
                ft.Card(
                    content=ft.Container(
                        content=ft.Column([
                            ft.Text("File Selection", size=18, weight=ft.FontWeight.BOLD),
                            ft.Row([excel_btn, self.excel_file_text], alignment=ft.MainAxisAlignment.START),
                            ft.Row([pdf_btn, self.pdf_file_text], alignment=ft.MainAxisAlignment.START),
                            ft.Divider(),
                            ft.Row([self.analyze_btn, self.progress_ring], alignment=ft.MainAxisAlignment.CENTER),
                        ]),
                        padding=20
                    ),
                    elevation=3
                ),
                
                ft.Container(height=20),
                
                ft.Card(
                    content=ft.Container(
                        content=ft.Column([
                            ft.Text("Analysis Results", size=18, weight=ft.FontWeight.BOLD),
                            self.results_container
                        ]),
                        padding=20
                    ),
                    elevation=3
                )
            ],
            scroll=ft.ScrollMode.AUTO,
            expand=True)
        )

    def pick_excel_file(self, e):
        """Handle Excel file selection"""
        file_picker = ft.FilePicker(on_result=self.excel_file_result)
        self.page.overlay.append(file_picker)
        self.page.update()
        file_picker.pick_files(
            dialog_title="Select Excel Timesheet",
            allowed_extensions=["xlsx", "xls"]
        )

    def excel_file_result(self, e: ft.FilePickerResultEvent):
        """Handle Excel file selection result"""
        if e.files:
            file_path = e.files[0].path
            self.excel_file_text.value = f"Selected: {Path(file_path).name}"
            self.analyzer.excel_file_path = file_path
            self.check_ready_to_analyze()
            self.page.update()

    def pick_pdf_file(self, e):
        """Handle PDF file selection"""
        file_picker = ft.FilePicker(on_result=self.pdf_file_result)
        self.page.overlay.append(file_picker)
        self.page.update()
        file_picker.pick_files(
            dialog_title="Select PDF Pay Slip",
            allowed_extensions=["pdf"]
        )

    def pdf_file_result(self, e: ft.FilePickerResultEvent):
        """Handle PDF file selection result"""
        if e.files:
            file_path = e.files[0].path
            self.pdf_file_text.value = f"Selected: {Path(file_path).name}"
            self.analyzer.pdf_file_path = file_path
            self.check_ready_to_analyze()
            self.page.update()

    def check_ready_to_analyze(self):
        """Check if both files are selected and enable analyze button"""
        if hasattr(self.analyzer, 'excel_file_path') and hasattr(self.analyzer, 'pdf_file_path'):
            self.analyze_btn.disabled = False
        self.page.update()

    def analyze_files(self, e):
        """Perform the analysis"""
        try:
            # Show progress
            self.progress_ring.visible = True
            self.analyze_btn.disabled = True
            self.page.update()

            # Parse files
            self.analyzer.excel_data = self.analyzer.parse_excel_timesheet(self.analyzer.excel_file_path)
            self.analyzer.pdf_data = self.analyzer.parse_pdf_payslip(self.analyzer.pdf_file_path)

            # Compare data
            comparison = self.analyzer.compare_data()

            # Display results
            self.display_results(comparison)

        except Exception as ex:
            self.show_error(f"Analysis failed: {str(ex)}")
        finally:
            # Hide progress
            self.progress_ring.visible = False
            self.analyze_btn.disabled = False
            self.page.update()

    def display_results(self, comparison: Dict):
        """Display analysis results"""
        excel_hours = comparison['excel_total_hours']
        pdf_hours = comparison['pdf_total_hours']
        difference = comparison['difference']
        match = comparison['match']

        # Determine status color
        status_color = ft.colors.GREEN if match else ft.colors.RED
        status_text = "✓ MATCH" if match else "✗ MISMATCH"

        results_content = ft.Column([
            ft.Row([
                ft.Text("Status:", weight=ft.FontWeight.BOLD),
                ft.Text(status_text, color=status_color, weight=ft.FontWeight.BOLD)
            ]),
            ft.Divider(),
            ft.Row([
                ft.Text("Excel Total Hours:", weight=ft.FontWeight.BOLD),
                ft.Text(f"{excel_hours:.1f}h")
            ]),
            ft.Row([
                ft.Text("PDF Paid Hours:", weight=ft.FontWeight.BOLD),
                ft.Text(f"{pdf_hours:.1f}h")
            ]),
            ft.Row([
                ft.Text("Difference:", weight=ft.FontWeight.BOLD),
                ft.Text(f"{difference:+.1f}h", color=status_color)
            ]),
            ft.Row([
                ft.Text("Percentage Diff:", weight=ft.FontWeight.BOLD),
                ft.Text(f"{comparison['percentage_difference']:+.1f}%", color=status_color)
            ]),
        ])

        self.results_container.content = results_content
        self.page.update()

    def show_error(self, message: str):
        """Show error message"""
        self.results_container.content = ft.Text(
            f"Error: {message}",
            color=ft.colors.RED,
            weight=ft.FontWeight.BOLD
        )
        self.page.update()


def main(page: ft.Page):
    """Main application entry point"""
    app = TimesheetApp(page)


if __name__ == "__main__":
    ft.app(target=main) 