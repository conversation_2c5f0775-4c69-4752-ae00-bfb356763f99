import flet as ft
import pandas as pd
import pdfplumber
import re
from datetime import datetime
from pathlib import Path
import logging
from typing import Dict, List, Tuple, Optional

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FixedTimesheetAnalyzer:
    """Fixed analyzer with improved parsing for actual data structure"""
    
    def __init__(self):
        # Day codes that represent paid working days (8 hours each)
        self.paid_day_codes = {
            'Z': 8,    # Regular work day
            'R': 8,    # Regular work day 
            'F': 8,    # Holiday/Festivity
            'FE': 8,   # Extra holiday
            'DI': 8,   # Something else (paid)
            'RD': 8,   # Reduced day but paid
            'ED': 8    # Extra day
        }
        self.excel_data = None
        self.pdf_data = None

    def parse_excel_timesheet(self, file_path: str) -> Dict:
        """Parse Excel timesheet - fixed for actual format"""
        try:
            # Read Excel file
            df = pd.read_excel(file_path, sheet_name=0, header=None)
            
            # Find employee name by looking for "CACACE LUCA" pattern
            employee_name = "Unknown"
            for idx, row in df.iterrows():
                for cell in row:
                    if pd.notna(cell) and isinstance(cell, str):
                        cell_upper = cell.upper().strip()
                        # Look for names with multiple uppercase words
                        if len(cell_upper.split()) >= 2 and all(word.isalpha() for word in cell_upper.split()):
                            if len(cell_upper) > 5:  # Reasonable name length
                                employee_name = cell_upper
                                break
                if employee_name != "Unknown":
                    break

            # Look for the row with day numbers by searching in a wider range
            day_header_row = None
            day_pattern = None
            
            for idx in range(len(df)):
                row = df.iloc[idx]
                consecutive_numbers = 0
                start_col = None
                
                # Check each cell in the row
                for col_idx, cell in enumerate(row):
                    if pd.notna(cell):
                        try:
                            val = str(cell).strip()
                            if val.isdigit():
                                num = int(val)
                                if 1 <= num <= 31:
                                    if start_col is None:
                                        start_col = col_idx
                                    consecutive_numbers += 1
                                else:
                                    break
                            else:
                                break
                        except:
                            break
                
                # If we found a good sequence of day numbers
                if consecutive_numbers >= 10:
                    day_header_row = idx
                    day_pattern = (start_col, consecutive_numbers)
                    print(f"Found day header at row {idx}, starting column {start_col}, {consecutive_numbers} days")
                    break

            if day_header_row is None:
                # Alternative approach: look for any row with many numeric values 1-31
                print("Primary search failed, trying alternative approach...")
                for idx in range(20, min(100, len(df))):
                    row = df.iloc[idx]
                    day_count = 0
                    for cell in row:
                        if pd.notna(cell):
                            try:
                                val = str(cell).strip()
                                if val.isdigit() and 1 <= int(val) <= 31:
                                    day_count += 1
                            except:
                                pass
                    if day_count >= 8:  # Lower threshold
                        day_header_row = idx
                        print(f"Alternative: Found day header at row {idx} with {day_count} day numbers")
                        break

            if day_header_row is None:
                raise ValueError("Could not find day header row in Excel file")

            # Extract activity data from rows after day header
            activities_data = {}
            total_hours_per_day = [0] * 31
            
            # Parse activities starting after day header
            for row_idx in range(day_header_row + 1, min(day_header_row + 50, len(df))):
                row = df.iloc[row_idx]
                
                # Get activity name from first non-empty cell
                activity_name = ""
                for cell in row[:5]:  # Check first few columns for activity name
                    if pd.notna(cell) and isinstance(cell, str) and len(str(cell).strip()) > 0:
                        activity_name = str(cell).strip()
                        break
                
                if activity_name and not activity_name.lower().startswith('totale'):
                    daily_hours = []
                    total_activity_hours = 0
                    
                    # Extract hours for each day
                    for day in range(31):
                        # Try multiple column offsets to find the data
                        hour_value = 0
                        for col_offset in [day + 1, day + 2, day + 3]:  # Try different column alignments
                            if col_offset < len(row):
                                cell_value = row.iloc[col_offset]
                                try:
                                    if pd.notna(cell_value) and str(cell_value).strip():
                                        potential_hours = float(cell_value)
                                        if 0 <= potential_hours <= 24:  # Reasonable hour range
                                            hour_value = potential_hours
                                            break
                                except:
                                    pass
                        
                        daily_hours.append(hour_value)
                        total_activity_hours += hour_value
                        
                        # Add to total if not unpaid absence
                        if 'assenza non retribuita' not in activity_name.lower():
                            total_hours_per_day[day] += hour_value
                    
                    # Only include activities with some hours
                    if total_activity_hours > 0:
                        activities_data[activity_name] = daily_hours

            # Calculate totals
            total_hours = sum(total_hours_per_day)
            working_days = sum(1 for hours in total_hours_per_day if hours > 0)

            print(f"Excel parsing results: {employee_name}, {total_hours}h, {working_days} days, {len(activities_data)} activities")

            return {
                'employee_name': employee_name,
                'activities': activities_data,
                'total_hours_per_day': total_hours_per_day,
                'total_hours': total_hours,
                'working_days': working_days,
                'file_path': file_path
            }

        except Exception as e:
            logger.error(f"Error parsing Excel file: {e}")
            raise

    def parse_pdf_payslip(self, file_path: str) -> Dict:
        """Parse PDF pay slip - fixed for actual format"""
        try:
            with pdfplumber.open(file_path) as pdf:
                full_text = ""
                for page in pdf.pages:
                    page_text = page.extract_text()
                    if page_text:
                        full_text += page_text + "\n"

            # Extract employee name - look for the pattern in the actual PDF
            employee_name = self._extract_employee_name_fixed(full_text)
            
            # Extract month/year
            month_year = self._extract_month_year_fixed(full_text)
            
            # Extract day codes from the specific table area
            day_codes_count = self._count_day_codes_fixed(full_text)
            
            # Calculate total paid hours (be more conservative)
            total_paid_hours = sum(count * hours for code, count in day_codes_count.items() 
                                 for code_key, hours in self.paid_day_codes.items() 
                                 if code == code_key)

            print(f"PDF parsing results: {employee_name}, {month_year}, {total_paid_hours}h, codes: {day_codes_count}")

            return {
                'employee_name': employee_name,
                'month_year': month_year,
                'day_codes_count': day_codes_count,
                'total_paid_hours': total_paid_hours,
                'total_paid_days': sum(day_codes_count.values()),
                'file_path': file_path
            }

        except Exception as e:
            logger.error(f"Error parsing PDF file: {e}")
            raise

    def _extract_employee_name_fixed(self, text: str) -> str:
        """Extract employee name from PDF text - fixed"""
        patterns = [
            r'(\d+)\s+([A-Z]+\s+[A-Z]+(?:\s+[A-Z]+)?)',  # Number followed by name like "49 BORRETTI GIOVANBATTISTA"
            r'COGNOME E NOME[\s\n]*(\d+)?\s*([A-Z\s]+)',
        ]
        
        lines = text.split('\n')
        for line in lines:
            line = line.strip()
            # Look for the specific pattern like "49 BORRETTI GIOVANBATTISTA"
            match = re.search(r'(\d+)\s+([A-Z]+\s+[A-Z]+)', line)
            if match:
                name = match.group(2).strip()
                if len(name) > 5 and ' ' in name:  # Valid name with space
                    return name
        
        # Fallback patterns
        for pattern in patterns:
            match = re.search(pattern, text)
            if match:
                if len(match.groups()) > 1:
                    name = match.group(2 if match.group(1).isdigit() else 1).strip()
                    if len(name) > 3:
                        return name
        
        return "Unknown Employee"

    def _extract_month_year_fixed(self, text: str) -> str:
        """Extract month and year from PDF text - fixed"""
        # Look for "Gennaio 2025" pattern
        match = re.search(r'(\w+)\s+(\d{4})', text)
        if match:
            return f"{match.group(1)} {match.group(2)}"
        
        # Look for other date patterns
        match = re.search(r'(\d{1,2})/(\d{4})', text)
        if match:
            return f"{match.group(1)}/{match.group(2)}"
        
        return "Unknown Period"

    def _count_day_codes_fixed(self, text: str) -> Dict[str, int]:
        """Count day codes more accurately by looking in the right section"""
        day_codes_count = {}
        
        # Split into lines and look for the timesheet/calendar section
        lines = text.split('\n')
        
        # Find the section with the calendar/day codes (usually after employee info)
        calendar_section_start = -1
        for i, line in enumerate(lines):
            # Look for indicators of the calendar section
            if any(keyword in line.upper() for keyword in ['VOCE', 'DESCRIZIONE', 'ORE', 'SOLA']):
                calendar_section_start = i
                break
        
        if calendar_section_start > -1:
            # Process the calendar section (next 30-50 lines)
            calendar_lines = lines[calendar_section_start:calendar_section_start + 50]
            calendar_text = ' '.join(calendar_lines)
            
            # Count codes in this specific section
            for code in self.paid_day_codes.keys():
                # Look for the code with surrounding spaces or punctuation
                pattern = rf'\b{re.escape(code)}\b'
                matches = re.findall(pattern, calendar_text)
                if matches:
                    # Be more conservative - limit to reasonable numbers
                    count = min(len(matches), 31)  # Max 31 days in a month
                    if count > 0:
                        day_codes_count[code] = count
        
        # If no codes found in calendar section, try a more general approach
        if not day_codes_count:
            for code in self.paid_day_codes.keys():
                pattern = rf'\b{re.escape(code)}\b'
                matches = re.findall(pattern, text)
                if matches:
                    # Very conservative count
                    count = min(len(matches), 25)
                    if count > 0:
                        day_codes_count[code] = count

        return day_codes_count

    def compare_data(self) -> Dict:
        """Compare Excel timesheet data with PDF pay slip data"""
        if not self.excel_data or not self.pdf_data:
            raise ValueError("Both Excel and PDF data must be loaded first")

        excel_hours = self.excel_data['total_hours']
        pdf_hours = self.pdf_data['total_paid_hours']
        difference = excel_hours - pdf_hours
        
        # Calculate percentage difference
        percentage_diff = (difference / pdf_hours * 100) if pdf_hours > 0 else 0
        
        # Determine if it's a match (allow reasonable tolerance)
        is_match = abs(difference) <= 16  # Allow up to 2 days difference
        
        return {
            'excel_employee': self.excel_data['employee_name'],
            'pdf_employee': self.pdf_data['employee_name'],
            'excel_total_hours': excel_hours,
            'pdf_total_hours': pdf_hours,
            'excel_working_days': self.excel_data['working_days'],
            'pdf_working_days': self.pdf_data['total_paid_days'],
            'difference_hours': difference,
            'percentage_difference': percentage_diff,
            'is_match': is_match,
            'month_year': self.pdf_data['month_year']
        }


class FixedTimesheetApp:
    """Fixed Flet GUI application"""
    
    def __init__(self, page: ft.Page):
        self.page = page
        self.analyzer = FixedTimesheetAnalyzer()
        self.setup_page()
        self.create_ui()

    def setup_page(self):
        """Configure the main page"""
        self.page.title = "Timesheet vs Pay Slip Analyzer"
        self.page.theme_mode = ft.ThemeMode.LIGHT
        self.page.window.width = 1000
        self.page.window.height = 800
        self.page.window.resizable = True
        self.page.padding = 20

    def create_ui(self):
        """Create the user interface"""
        # Main title
        title = ft.Text(
            "📊 Fixed Timesheet vs Pay Slip Analyzer",
            size=28,
            weight=ft.FontWeight.BOLD,
            color=ft.Colors.BLUE_800
        )

        # Description
        description = ft.Text(
            "Compare Excel timesheets with PDF pay slips - Fixed Version",
            size=16,
            color=ft.Colors.GREY_700
        )

        # File selection section
        self.excel_file_display = ft.Text("No Excel file selected", color=ft.Colors.GREY_600)
        self.pdf_file_display = ft.Text("No PDF file selected", color=ft.Colors.GREY_600)
        
        excel_pick_btn = ft.ElevatedButton(
            "📁 Select Excel Timesheet",
            icon=ft.Icons.FOLDER_OPEN,
            on_click=self.pick_excel_file,
            style=ft.ButtonStyle(
                bgcolor=ft.Colors.BLUE_600,
                color=ft.Colors.WHITE
            )
        )
        
        pdf_pick_btn = ft.ElevatedButton(
            "📄 Select PDF Pay Slip",
            icon=ft.Icons.PICTURE_AS_PDF,
            on_click=self.pick_pdf_file,
            style=ft.ButtonStyle(
                bgcolor=ft.Colors.RED_600,
                color=ft.Colors.WHITE
            )
        )

        # Quick test button for current files
        test_current_btn = ft.ElevatedButton(
            "⚡ Test Current Files",
            icon=ft.Icons.PLAY_ARROW,
            on_click=self.test_current_files,
            style=ft.ButtonStyle(
                bgcolor=ft.Colors.ORANGE_600,
                color=ft.Colors.WHITE
            )
        )

        # Analysis button
        self.analyze_btn = ft.ElevatedButton(
            "🔍 Analyze & Compare",
            icon=ft.Icons.ANALYTICS,
            on_click=self.analyze_files,
            disabled=True,
            style=ft.ButtonStyle(
                bgcolor=ft.Colors.GREEN_600,
                color=ft.Colors.WHITE,
                text_style=ft.TextStyle(size=16, weight=ft.FontWeight.BOLD)
            )
        )

        # Progress indicator
        self.progress_ring = ft.ProgressRing(visible=False, color=ft.Colors.BLUE_600)
        self.status_text = ft.Text("", color=ft.Colors.GREY_600)

        # Results section
        self.results_container = ft.Container(
            content=ft.Column([
                ft.Text("📈 Analysis Results", size=18, weight=ft.FontWeight.BOLD),
                ft.Text("Results will appear here after analysis", color=ft.Colors.GREY_600)
            ]),
            padding=20,
            border=ft.border.all(2, ft.Colors.GREY_300),
            border_radius=10,
            bgcolor=ft.Colors.GREY_50
        )

        # Main layout
        self.page.add(
            ft.Column([
                # Header
                ft.Container(
                    content=ft.Column([title, description]),
                    padding=ft.padding.only(bottom=30)
                ),
                
                # File selection card
                ft.Card(
                    content=ft.Container(
                        content=ft.Column([
                            ft.Text("📁 File Selection", size=20, weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE_700),
                            ft.Divider(height=1),
                            
                            # Excel file row
                            ft.Row([
                                excel_pick_btn,
                                ft.VerticalDivider(width=20),
                                ft.Container(content=self.excel_file_display, expand=True)
                            ], alignment=ft.MainAxisAlignment.START),

                            # PDF file row
                            ft.Row([
                                pdf_pick_btn,
                                ft.VerticalDivider(width=20),
                                ft.Container(content=self.pdf_file_display, expand=True)
                            ], alignment=ft.MainAxisAlignment.START),

                            ft.Divider(height=1),

                            # Analysis button row
                            ft.Row([
                                test_current_btn,
                                self.analyze_btn,
                                self.progress_ring,
                                ft.Container(content=self.status_text, expand=True)
                            ], alignment=ft.MainAxisAlignment.CENTER),
                        ]),
                        padding=25
                    ),
                    elevation=5
                ),
                
                ft.Container(height=20),
                
                # Results card
                ft.Card(
                    content=ft.Container(
                        content=self.results_container,
                        padding=5
                    ),
                    elevation=5
                )
            ],
            scroll=ft.ScrollMode.AUTO,
            expand=True)
        )

    def test_current_files(self, e):
        """Test with the current files in directory"""
        self.analyzer.excel_file_path = "GENNAIO 2025.xlsx"
        self.analyzer.pdf_file_path = "D0531_STAMPA LUL_01.2025.pdf"
        self.excel_file_display.value = "✅ GENNAIO 2025.xlsx"
        self.excel_file_display.color = ft.Colors.GREEN_600
        self.pdf_file_display.value = "✅ D0531_STAMPA LUL_01.2025.pdf"
        self.pdf_file_display.color = ft.Colors.GREEN_600
        self.analyze_btn.disabled = False
        self.page.update()
        
        # Automatically start analysis
        self.analyze_files(e)

    def pick_excel_file(self, e):
        """Handle Excel file selection"""
        file_picker = ft.FilePicker(on_result=self.excel_file_result)
        self.page.overlay.append(file_picker)
        self.page.update()
        file_picker.pick_files(
            dialog_title="Select Excel Timesheet File",
            allowed_extensions=["xlsx", "xls"]
        )

    def excel_file_result(self, e: ft.FilePickerResultEvent):
        """Handle Excel file selection result"""
        if e.files:
            file_path = e.files[0].path
            filename = Path(file_path).name
            self.excel_file_display.value = f"✅ {filename}"
            self.excel_file_display.color = ft.Colors.GREEN_600
            self.analyzer.excel_file_path = file_path
            self.check_ready_to_analyze()
            self.page.update()

    def pick_pdf_file(self, e):
        """Handle PDF file selection"""
        file_picker = ft.FilePicker(on_result=self.pdf_file_result)
        self.page.overlay.append(file_picker)
        self.page.update()
        file_picker.pick_files(
            dialog_title="Select PDF Pay Slip File",
            allowed_extensions=["pdf"]
        )

    def pdf_file_result(self, e: ft.FilePickerResultEvent):
        """Handle PDF file selection result"""
        if e.files:
            file_path = e.files[0].path
            filename = Path(file_path).name
            self.pdf_file_display.value = f"✅ {filename}"
            self.pdf_file_display.color = ft.Colors.GREEN_600
            self.analyzer.pdf_file_path = file_path
            self.check_ready_to_analyze()
            self.page.update()

    def check_ready_to_analyze(self):
        """Check if both files are selected and enable analyze button"""
        if (hasattr(self.analyzer, 'excel_file_path') and 
            hasattr(self.analyzer, 'pdf_file_path')):
            self.analyze_btn.disabled = False
            self.status_text.value = "Ready to analyze!"
            self.status_text.color = ft.Colors.GREEN_600
        self.page.update()

    def analyze_files(self, e):
        """Perform the analysis"""
        try:
            # Show progress
            self.progress_ring.visible = True
            self.analyze_btn.disabled = True
            self.status_text.value = "Analyzing files..."
            self.status_text.color = ft.Colors.BLUE_600
            self.page.update()

            # Parse Excel file
            self.status_text.value = "Parsing Excel timesheet..."
            self.page.update()
            self.analyzer.excel_data = self.analyzer.parse_excel_timesheet(self.analyzer.excel_file_path)

            # Parse PDF file
            self.status_text.value = "Parsing PDF pay slip..."
            self.page.update()
            self.analyzer.pdf_data = self.analyzer.parse_pdf_payslip(self.analyzer.pdf_file_path)

            # Compare data
            self.status_text.value = "Comparing data..."
            self.page.update()
            comparison = self.analyzer.compare_data()

            # Display results
            self.display_results(comparison)
            
            self.status_text.value = "Analysis complete!"
            self.status_text.color = ft.Colors.GREEN_600

        except Exception as ex:
            self.show_error(f"Analysis failed: {str(ex)}")
        finally:
            # Hide progress
            self.progress_ring.visible = False
            self.analyze_btn.disabled = False
            self.page.update()

    def display_results(self, comparison: Dict):
        """Display detailed analysis results"""
        is_match = comparison['is_match']
        status_icon = "✅" if is_match else "❌"
        status_text = "MATCH" if is_match else "MISMATCH"
        status_color = ft.Colors.GREEN if is_match else ft.Colors.RED

        # Create results content
        results_content = ft.Column([
            # Header
            ft.Row([
                ft.Text("📈 Analysis Results", size=20, weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE_700),
                ft.Container(expand=True),
                ft.Container(
                    content=ft.Text(f"{status_icon} {status_text}", size=18, weight=ft.FontWeight.BOLD, color=status_color),
                    bgcolor=status_color.with_opacity(0.1),
                    padding=10,
                    border_radius=5
                )
            ]),
            
            ft.Divider(height=2),
            
            # Employee info
            ft.Row([
                ft.Column([
                    ft.Text("📋 Excel Employee:", weight=ft.FontWeight.BOLD),
                    ft.Text(comparison['excel_employee'], color=ft.Colors.BLUE_700)
                ], expand=True),
                ft.Column([
                    ft.Text("📄 PDF Employee:", weight=ft.FontWeight.BOLD),
                    ft.Text(comparison['pdf_employee'], color=ft.Colors.RED_700)
                ], expand=True),
                ft.Column([
                    ft.Text("📅 Period:", weight=ft.FontWeight.BOLD),
                    ft.Text(comparison['month_year'], color=ft.Colors.GREY_700)
                ], expand=True)
            ]),
            
            ft.Divider(height=1),
            
            # Hours comparison
            ft.Row([
                ft.Column([
                    ft.Text("⏰ Excel Total Hours:", weight=ft.FontWeight.BOLD),
                    ft.Text(f"{comparison['excel_total_hours']:.1f}h", size=24, color=ft.Colors.BLUE_700, weight=ft.FontWeight.BOLD)
                ], expand=True),
                ft.Column([
                    ft.Text("💰 PDF Paid Hours:", weight=ft.FontWeight.BOLD),
                    ft.Text(f"{comparison['pdf_total_hours']:.1f}h", size=24, color=ft.Colors.RED_700, weight=ft.FontWeight.BOLD)
                ], expand=True),
                ft.Column([
                    ft.Text("📊 Difference:", weight=ft.FontWeight.BOLD),
                    ft.Text(f"{comparison['difference_hours']:+.1f}h", size=24, color=status_color, weight=ft.FontWeight.BOLD)
                ], expand=True)
            ]),
            
            # Days comparison
            ft.Row([
                ft.Column([
                    ft.Text("📅 Excel Working Days:", weight=ft.FontWeight.BOLD),
                    ft.Text(f"{comparison['excel_working_days']}", size=18, color=ft.Colors.BLUE_700)
                ], expand=True),
                ft.Column([
                    ft.Text("📅 PDF Paid Days:", weight=ft.FontWeight.BOLD),
                    ft.Text(f"{comparison['pdf_working_days']}", size=18, color=ft.Colors.RED_700)
                ], expand=True),
                ft.Column([
                    ft.Text("📈 Percentage Diff:", weight=ft.FontWeight.BOLD),
                    ft.Text(f"{comparison['percentage_difference']:+.1f}%", size=18, color=status_color)
                ], expand=True)
            ])
        ])

        self.results_container.content = results_content
        self.page.update()

    def show_error(self, message: str):
        """Show error message"""
        error_content = ft.Column([
            ft.Text("❌ Error", size=20, weight=ft.FontWeight.BOLD, color=ft.Colors.RED),
            ft.Divider(height=1),
            ft.Text(message, color=ft.Colors.RED_700),
        ])
        
        self.results_container.content = error_content
        self.status_text.value = "Analysis failed!"
        self.status_text.color = ft.Colors.RED
        self.page.update()


def main(page: ft.Page):
    """Main application entry point"""
    app = FixedTimesheetApp(page)


if __name__ == "__main__":
    ft.app(target=main) 